<!-- <PERSON><PERSON>er -->
<div class="sticky-header">
  <div class="container-fluid">
    <!-- Header -->
    <div class="modern-card mb-4">
      <div class="modern-card-header">
        <div class="d-flex align-items-center">
          <button
            class="modern-btn modern-btn-outline-secondary me-3"
            (click)="goBack()">
            <fa-icon [icon]="faArrowLeft"></fa-icon>
          </button>
          <div>
            <h4 class="mb-0">Yeni Salon Ekleme</h4>
            <small class="text-muted">{{getStepTitle()}} - {{getStepDescription()}}</small>
          </div>
        </div>
        <div class="d-flex gap-2">
          <button
            type="button"
            class="modern-btn modern-btn-secondary"
            (click)="goBack()">
            İptal
          </button>
          <button
            *ngIf="currentStep < WizardStep.PREVIEW"
            type="button"
            class="modern-btn modern-btn-outline-primary"
            [disabled]="currentStep === WizardStep.COMPANY_INFO"
            (click)="previousStep()">
            <fa-icon [icon]="faArrowLeft" class="me-1"></fa-icon>
            Önceki
          </button>
          <button
            *ngIf="currentStep < WizardStep.PREVIEW"
            type="button"
            class="modern-btn modern-btn-primary"
            (click)="nextStep()">
            Sonraki
            <fa-icon [icon]="faArrowRight" class="ms-1"></fa-icon>
          </button>
          <button
            *ngIf="currentStep === WizardStep.PREVIEW"
            type="button"
            class="modern-btn modern-btn-success"
            [disabled]="isSubmitting || !canSubmitCompany()"
            (click)="onSubmit()">
            <fa-icon [icon]="faPlus" class="me-1"></fa-icon>
            {{ isSubmitting ? 'Oluşturuluyor...' : 'Salon Oluştur' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="main-content">
  <div class="container-fluid">
    
    <!-- Step Navigation -->
    <div class="modern-card mb-4">
      <div class="modern-card-body py-3">
        <div class="row g-2">
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.COMPANY_INFO)"
              [disabled]="!canGoToStep(WizardStep.COMPANY_INFO)"
              (click)="goToStep(WizardStep.COMPANY_INFO)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.COMPANY_INFO)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">1. Şirket Bilgileri</div>
                  <small class="text-muted">Salon bilgileri</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.ADDRESS_INFO)"
              [disabled]="!canGoToStep(WizardStep.ADDRESS_INFO)"
              (click)="goToStep(WizardStep.ADDRESS_INFO)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.ADDRESS_INFO)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">2. Adres Bilgileri</div>
                  <small class="text-muted">Konum bilgileri</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.OWNER_INFO)"
              [disabled]="!canGoToStep(WizardStep.OWNER_INFO)"
              (click)="goToStep(WizardStep.OWNER_INFO)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.OWNER_INFO)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">3. Salon Sahibi</div>
                  <small class="text-muted">Sahip bilgileri</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.PREVIEW)"
              [disabled]="!canGoToStep(WizardStep.PREVIEW)"
              (click)="goToStep(WizardStep.PREVIEW)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.PREVIEW)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">4. Önizleme</div>
                  <small class="text-muted">Kontrol ve onay</small>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 1: Company Information -->
    <div *ngIf="currentStep === WizardStep.COMPANY_INFO" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
          Şirket Bilgileri
        </h5>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="companyForm">
          <div class="row g-3">
            <!-- Company Name -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Şirket Adı <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="companyForm.get('companyName')?.invalid && companyForm.get('companyName')?.touched"
                  formControlName="companyName"
                  placeholder="Örn: Fitness Center">
                <div class="invalid-feedback" 
                     *ngIf="companyForm.get('companyName')?.invalid && companyForm.get('companyName')?.touched">
                  Şirket adı en az 2 karakter olmalıdır
                </div>
              </div>
            </div>

            <!-- Company Phone -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Şirket Telefonu <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="companyForm.get('companyPhone')?.invalid && companyForm.get('companyPhone')?.touched"
                  formControlName="companyPhone"
                  placeholder="Örn: 05551234567">
                <div class="invalid-feedback" 
                     *ngIf="companyForm.get('companyPhone')?.invalid && companyForm.get('companyPhone')?.touched">
                  Geçerli bir telefon numarası giriniz (10-11 haneli)
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 2: Address Information -->
    <div *ngIf="currentStep === WizardStep.ADDRESS_INFO" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>
          Adres Bilgileri
        </h5>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="addressForm">
          <div class="row g-3">
            
            <!-- City Selection -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Şehir <span class="text-danger">*</span>
                </label>
                <mat-form-field appearance="outline" class="modern-mat-form-field">
                  <input type="text" 
                         matInput 
                         formControlName="city" 
                         [matAutocomplete]="autoCity" 
                         placeholder="Şehir seçiniz">
                  <mat-icon matPrefix>location_city</mat-icon>
                  <mat-autocomplete #autoCity="matAutocomplete" [displayWith]="displayCity" (optionSelected)="onCitySelected($event.option.value)">
                    <mat-option *ngFor="let city of filteredCities | async" [value]="city">
                      {{city.cityName}}
                    </mat-option>
                  </mat-autocomplete>
                  <mat-error *ngIf="addressForm.get('city')?.hasError('required')">
                    Şehir seçimi zorunludur
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <!-- Town Selection -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  İlçe <span class="text-danger">*</span>
                </label>
                <mat-form-field appearance="outline" class="modern-mat-form-field">
                  <input type="text" 
                         matInput 
                         formControlName="town" 
                         [matAutocomplete]="autoTown" 
                         placeholder="İlçe seçiniz"
                         [disabled]="!addressForm.get('city')?.value?.cityID">
                  <mat-icon matPrefix>location_on</mat-icon>
                  <mat-autocomplete #autoTown="matAutocomplete" [displayWith]="displayTown">
                    <mat-option *ngFor="let town of filteredTowns | async" [value]="town">
                      {{town.townName}}
                    </mat-option>
                  </mat-autocomplete>
                  <mat-error *ngIf="addressForm.get('town')?.hasError('required')">
                    İlçe seçimi zorunludur
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <!-- Detailed Address -->
            <div class="col-12">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Detay Adres <span class="text-danger">*</span>
                </label>
                <textarea 
                  class="modern-form-control"
                  formControlName="address"
                  rows="3"
                  placeholder="Mahalle, sokak, bina no vb. detay adres bilgilerini giriniz">
                </textarea>
                <div class="invalid-feedback" 
                     *ngIf="addressForm.get('address')?.invalid && addressForm.get('address')?.touched">
                  Detay adres zorunludur
                </div>
              </div>
            </div>

          </div>
        </form>
      </div>
    </div>

    <!-- Step 3: Owner Information -->
    <div *ngIf="currentStep === WizardStep.OWNER_INFO" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faUserTie" class="me-2"></fa-icon>
          Salon Sahibi Bilgileri
        </h5>
        <p class="mb-0 mt-2 text-muted">
          <small>Salon sahibi bilgilerini giriniz. E-posta kontrolü otomatik yapılacaktır.</small>
        </p>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="ownerForm">
          <div class="row g-3">

            <!-- First Name -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Ad <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="ownerForm.get('firstName')?.invalid && ownerForm.get('firstName')?.touched"
                  formControlName="firstName"
                  placeholder="Örn: Ahmet">
                <div class="invalid-feedback"
                     *ngIf="ownerForm.get('firstName')?.invalid && ownerForm.get('firstName')?.touched">
                  Ad en az 2 karakter olmalıdır
                </div>
              </div>
            </div>

            <!-- Last Name -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Soyad <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="ownerForm.get('lastName')?.invalid && ownerForm.get('lastName')?.touched"
                  formControlName="lastName"
                  placeholder="Örn: Yılmaz">
                <div class="invalid-feedback"
                     *ngIf="ownerForm.get('lastName')?.invalid && ownerForm.get('lastName')?.touched">
                  Soyad en az 2 karakter olmalıdır
                </div>
              </div>
            </div>

            <!-- Email -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  E-posta <span class="text-danger">*</span>
                </label>
                <input
                  type="email"
                  class="modern-form-control"
                  [class.is-invalid]="ownerForm.get('email')?.invalid && ownerForm.get('email')?.touched"
                  formControlName="email"
                  placeholder="Örn: <EMAIL>"
                  (blur)="checkEmailExists()">
                <div class="invalid-feedback"
                     *ngIf="ownerForm.get('email')?.invalid && ownerForm.get('email')?.touched">
                  <span *ngIf="ownerForm.get('email')?.hasError('required')">E-posta zorunludur</span>
                  <span *ngIf="ownerForm.get('email')?.hasError('email')">Geçerli bir e-posta adresi giriniz</span>
                  <span *ngIf="ownerForm.get('email')?.hasError('emailExists')">Bu e-posta adresi sistemde zaten kayıtlı!</span>
                </div>
              </div>
            </div>

            <!-- Phone Number -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Telefon Numarası <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="ownerForm.get('phoneNumber')?.invalid && ownerForm.get('phoneNumber')?.touched"
                  formControlName="phoneNumber"
                  placeholder="Örn: 05551234567">
                <div class="invalid-feedback"
                     *ngIf="ownerForm.get('phoneNumber')?.invalid && ownerForm.get('phoneNumber')?.touched">
                  Geçerli bir telefon numarası giriniz (10-11 haneli)
                </div>
                <small class="form-text text-muted">
                  Geçici şifre telefon numarasının son 4 hanesi olacaktır
                </small>
              </div>
            </div>

          </div>
        </form>
      </div>
    </div>

    <!-- Step 4: Preview -->
    <div *ngIf="currentStep === WizardStep.PREVIEW" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faEye" class="me-2"></fa-icon>
          Önizleme ve Onay
        </h5>
        <p class="mb-0 mt-2 text-muted">
          <small>Girdiğiniz bilgileri kontrol edin ve onaylayın</small>
        </p>
      </div>
      <div class="modern-card-body">
        <div class="row g-4">

          <!-- Company Info Preview -->
          <div class="col-md-4">
            <div class="preview-section">
              <h6 class="preview-title">
                <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
                Şirket Bilgileri
              </h6>
              <div class="preview-content">
                <div class="preview-item">
                  <strong>Şirket Adı:</strong>
                  <span>{{ companyData.companyName || '-' }}</span>
                </div>
                <div class="preview-item">
                  <strong>Telefon:</strong>
                  <span>{{ companyData.companyPhone || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Address Info Preview -->
          <div class="col-md-4">
            <div class="preview-section">
              <h6 class="preview-title">
                <fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>
                Adres Bilgileri
              </h6>
              <div class="preview-content">
                <div class="preview-item">
                  <strong>Şehir:</strong>
                  <span>{{ companyData.city?.cityName || '-' }}</span>
                </div>
                <div class="preview-item">
                  <strong>İlçe:</strong>
                  <span>{{ companyData.town?.townName || '-' }}</span>
                </div>
                <div class="preview-item">
                  <strong>Adres:</strong>
                  <span>{{ companyData.address || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Owner Info Preview -->
          <div class="col-md-4">
            <div class="preview-section">
              <h6 class="preview-title">
                <fa-icon [icon]="faUserTie" class="me-2"></fa-icon>
                Salon Sahibi
              </h6>
              <div class="preview-content">
                <div class="preview-item">
                  <strong>Ad Soyad:</strong>
                  <span>{{ companyData.owner?.firstName }} {{ companyData.owner?.lastName }}</span>
                </div>
                <div class="preview-item">
                  <strong>E-posta:</strong>
                  <span>{{ companyData.owner?.email || '-' }}</span>
                </div>
                <div class="preview-item">
                  <strong>Telefon:</strong>
                  <span>{{ companyData.owner?.phoneNumber || '-' }}</span>
                </div>
                <div class="preview-item">
                  <strong>Geçici Şifre:</strong>
                  <span class="text-muted">{{ companyData.owner?.phoneNumber?.slice(-4) || '****' }}</span>
                </div>
              </div>
            </div>
          </div>

        </div>

        <!-- Warning Alert -->
        <div class="alert alert-info mt-4">
          <fa-icon [icon]="faInfoCircle" class="me-2"></fa-icon>
          <strong>Bilgilendirme:</strong> Salon sahibi için otomatik kullanıcı hesabı oluşturulacak ve geçici şifre telefon numarasının son 4 hanesi olacaktır. İlk girişte şifre değiştirme zorunlu olacaktır.
        </div>
      </div>
    </div>

  </div>
</div>
