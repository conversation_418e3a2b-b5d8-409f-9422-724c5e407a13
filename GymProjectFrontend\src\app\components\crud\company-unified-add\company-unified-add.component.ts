import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { CompanyService } from '../../../services/company.service';
import { CompanyadressService } from '../../../services/companyadress.service';
import { CompanyUserService } from '../../../services/company-user.service';
import { UserCompanyService } from '../../../services/usercompany.service';
import { CityService } from '../../../services/city.service';
import { TownService } from '../../../services/town.service';
import { AuthService } from '../../../services/auth.service';
import { UserService } from '../../../services/user-service.service';
import { City } from '../../../models/city';
import { Town } from '../../../models/town';
import { Company } from '../../../models/company';
import { CompanyAdress } from '../../../models/companyAdress';
import { CompanyUser } from '../../../models/companyUser';
import { UserCompany } from '../../../models/usercompany';
import { User } from '../../../models/user';
import {
  faArrowLeft,
  faArrowRight,
  faCheck,
  faInfoCircle,
  faMapMarkerAlt,
  faUserTie,
  faEye,
  faBuilding,
  faPlus
} from '@fortawesome/free-solid-svg-icons';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';

// Wizard adımları enum'u
enum WizardStep {
  COMPANY_INFO = 1,    // Şirket Bilgileri
  ADDRESS_INFO = 2,    // Adres Bilgileri
  OWNER_INFO = 3,      // Salon Sahibi Bilgileri
  PREVIEW = 4          // Önizleme ve Onay
}

@Component({
  selector: 'app-company-unified-add',
  templateUrl: './company-unified-add.component.html',
  styleUrls: ['./company-unified-add.component.css'],
  standalone: false
})
export class CompanyUnifiedAddComponent implements OnInit {
  // Icons
  faArrowLeft = faArrowLeft;
  faArrowRight = faArrowRight;
  faCheck = faCheck;
  faInfoCircle = faInfoCircle;
  faMapMarkerAlt = faMapMarkerAlt;
  faUserTie = faUserTie;
  faEye = faEye;
  faBuilding = faBuilding;
  faPlus = faPlus;

  // Wizard state
  currentStep: WizardStep = WizardStep.COMPANY_INFO;
  WizardStep = WizardStep; // Template'de kullanmak için
  completedSteps: Set<WizardStep> = new Set();

  // Forms
  companyForm!: FormGroup;
  addressForm!: FormGroup;
  ownerForm!: FormGroup;
  isSubmitting = false;

  // Data
  companyData: any = {};
  cities: City[] = [];
  towns: Town[] = [];

  // Filtered observables
  filteredCities!: Observable<City[]>;
  filteredTowns!: Observable<Town[]>;

  constructor(
    private formBuilder: FormBuilder,
    private companyService: CompanyService,
    private companyAdressService: CompanyadressService,
    private companyUserService: CompanyUserService,
    private userCompanyService: UserCompanyService,
    private cityService: CityService,
    private townService: TownService,
    private toastrService: ToastrService,
    private authService: AuthService,
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.getCities();
    this.setupCityAutocomplete();
  }

  initializeForms(): void {
    // Adım 1: Şirket bilgileri formu
    this.companyForm = this.formBuilder.group({
      companyName: ['', [Validators.required, Validators.minLength(2)]],
      companyPhone: ['', [Validators.required, Validators.pattern(/^[0-9]{10,11}$/)]]
    });

    // Adım 2: Adres bilgileri formu
    this.addressForm = this.formBuilder.group({
      city: ['', Validators.required],
      town: ['', Validators.required],
      address: ['', Validators.required]
    });

    // Adım 3: Salon sahibi bilgileri formu
    this.ownerForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10,11}$/)]]
    });
  }

  setupCityAutocomplete(): void {
    this.filteredCities = this.addressForm.get('city')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.cityName),
      map(name => name ? this._filterCities(name) : this.cities.slice())
    );

    this.filteredTowns = this.addressForm.get('town')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.townName),
      map(name => name ? this._filterTowns(name) : this.towns.slice())
    );
  }

  // Wizard navigation methods
  nextStep(): void {
    if (this.canProceedToNextStep()) {
      // Mevcut adımı tamamlandı olarak işaretle
      this.completedSteps.add(this.currentStep);

      if (this.currentStep < WizardStep.PREVIEW) {
        this.currentStep++;
        this.onStepChange();
      }
    } else {
      this.toastrService.error('Lütfen işaretli alanları doldurunuz', 'Eksik Bilgi');
      this.validateAndHighlightErrors();
    }
  }

  previousStep(): void {
    if (this.currentStep > WizardStep.COMPANY_INFO) {
      this.currentStep--;
    }
  }

  goToStep(step: WizardStep): void {
    if (this.canGoToStep(step)) {
      this.currentStep = step;
      this.onStepChange();
    }
  }

  canGoToStep(step: WizardStep): boolean {
    // Sadece tamamlanmış adımlara veya bir sonraki adıma gidebilir
    return step <= this.currentStep || this.completedSteps.has(step - 1);
  }

  onStepChange(): void {
    switch (this.currentStep) {
      case WizardStep.ADDRESS_INFO:
        this.saveCompanyInfo();
        break;
      case WizardStep.OWNER_INFO:
        this.saveAddressInfo();
        break;
      case WizardStep.PREVIEW:
        this.saveOwnerInfo();
        this.preparePreviewData();
        break;
    }
  }

  // Step validation methods
  canProceedToNextStep(): boolean {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return this.companyForm.valid;
      case WizardStep.ADDRESS_INFO:
        return this.addressForm.valid && this.isAddressFormComplete();
      case WizardStep.OWNER_INFO:
        return this.ownerForm.valid;
      default:
        return true;
    }
  }

  isAddressFormComplete(): boolean {
    const city = this.addressForm.get('city')?.value;
    const town = this.addressForm.get('town')?.value;
    const address = this.addressForm.get('address')?.value;

    return !!(city?.cityID && town?.townID && address?.trim());
  }

  isStepCompleted(step: WizardStep): boolean {
    return this.completedSteps.has(step);
  }

  getStepButtonClass(step: WizardStep): string {
    if (this.currentStep === step) {
      return 'btn-primary'; // Aktif adım - mavi
    } else if (this.isStepCompleted(step)) {
      return 'btn-outline-success'; // Tamamlanmış adım - yeşil çerçeve
    } else {
      return 'btn-outline-secondary'; // Henüz erişilmemiş adım - gri
    }
  }

  getStepIcon(step: WizardStep): any {
    if (this.isStepCompleted(step)) {
      return this.faCheck;
    }

    switch (step) {
      case WizardStep.COMPANY_INFO:
        return this.faBuilding;
      case WizardStep.ADDRESS_INFO:
        return this.faMapMarkerAlt;
      case WizardStep.OWNER_INFO:
        return this.faUserTie;
      case WizardStep.PREVIEW:
        return this.faEye;
      default:
        return this.faInfoCircle;
    }
  }

  // Data saving methods
  saveCompanyInfo(): void {
    if (this.companyForm.valid) {
      this.companyData = {
        ...this.companyData,
        ...this.companyForm.value
      };
    }
  }

  saveAddressInfo(): void {
    if (this.addressForm.valid && this.isAddressFormComplete()) {
      this.companyData = {
        ...this.companyData,
        city: this.addressForm.get('city')?.value,
        town: this.addressForm.get('town')?.value,
        address: this.addressForm.get('address')?.value
      };
    }
  }

  saveOwnerInfo(): void {
    if (this.ownerForm.valid) {
      this.companyData = {
        ...this.companyData,
        owner: this.ownerForm.value
      };
    }
  }

  preparePreviewData(): void {
    // Preview için tüm verileri hazırla
    console.log('Preview data:', this.companyData);
  }



  // Şehir seçildiğinde ilçeleri getir
  onCitySelected(selectedCity: City): void {
    if (selectedCity && selectedCity.cityID) {
      // İlçe listesini temizle ve form kontrolünü sıfırla
      this.towns = [];
      this.addressForm.get('town')?.setValue('');

      // Seçilen şehre ait ilçeleri getir
      this.townService.getByCityId(selectedCity.cityID).subscribe(
        (response) => {
          if (response.success) {
            this.towns = response.data;
            // Filtrelenmiş ilçeleri güncelle
            this.filteredTowns = this.addressForm.get('town')!.valueChanges.pipe(
              startWith(''),
              map(value => typeof value === 'string' ? value : value?.townName),
              map(name => name ? this._filterTowns(name) : this.towns.slice())
            );
          }
        },
        (error) => {
          console.error('İlçeler yüklenirken hata oluştu:', error);
          this.toastrService.error('İlçeler yüklenirken hata oluştu', 'Hata');
        }
      );
    }
  }

  // E-posta kontrolü
  checkEmailExists(): void {
    const email = this.ownerForm.get('email')?.value;
    if (email && email.includes('@')) {
      this.userService.getByEmail(email).subscribe(
        (response) => {
          if (response.success && response.data) {
            this.toastrService.warning('Bu e-posta adresi sistemde zaten kayıtlı!', 'Uyarı');
            this.ownerForm.get('email')?.setErrors({ 'emailExists': true });
          }
        },
        (error) => {
          // E-posta bulunamadı, bu normal
          console.log('E-posta kontrolü: E-posta sistemde yok, devam edilebilir');
        }
      );
    }
  }

  // Utility methods
  getCities(): void {
    this.cityService.getCities().subscribe((response) => {
      this.cities = response.data;
    });
  }

  displayCity(city: City): string {
    return city && city.cityName ? city.cityName : '';
  }

  displayTown(town: Town): string {
    return town && town.townName ? town.townName : '';
  }

  private _filterCities(name: string): City[] {
    const filterValue = name.toLowerCase();
    return this.cities.filter(city => city.cityName.toLowerCase().includes(filterValue));
  }

  private _filterTowns(name: string): Town[] {
    const filterValue = name.toLowerCase();
    return this.towns.filter(town => town.townName.toLowerCase().includes(filterValue));
  }

  getStepTitle(): string {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return 'Şirket Bilgileri';
      case WizardStep.ADDRESS_INFO:
        return 'Adres Bilgileri';
      case WizardStep.OWNER_INFO:
        return 'Salon Sahibi Bilgileri';
      case WizardStep.PREVIEW:
        return 'Önizleme ve Onay';
      default:
        return '';
    }
  }

  getStepDescription(): string {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return 'Salon bilgilerini giriniz';
      case WizardStep.ADDRESS_INFO:
        return 'Salon adres bilgilerini giriniz';
      case WizardStep.OWNER_INFO:
        return 'Salon sahibi bilgilerini giriniz';
      case WizardStep.PREVIEW:
        return 'Bilgileri kontrol edin ve onaylayın';
      default:
        return '';
    }
  }

  goBack(): void {
    this.router.navigate(['/']);
  }

  validateAndHighlightErrors(): void {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        this.markFormGroupTouched(this.companyForm);
        break;
      case WizardStep.ADDRESS_INFO:
        this.markFormGroupTouched(this.addressForm);
        break;
      case WizardStep.OWNER_INFO:
        this.markFormGroupTouched(this.ownerForm);
        break;
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  // Final submission method (will be implemented later)
  onSubmit(): void {
    if (this.canSubmitCompany()) {
      this.isSubmitting = true;
      // Implementation will be added later
      console.log('Final company data:', this.companyData);
      this.toastrService.info('Salon oluşturma işlemi henüz implement edilmedi', 'Bilgi');
      this.isSubmitting = false;
    } else {
      this.toastrService.warning('Lütfen tüm adımları tamamlayın', 'Uyarı');
    }
  }

  canSubmitCompany(): boolean {
    return this.completedSteps.has(WizardStep.COMPANY_INFO) &&
           this.completedSteps.has(WizardStep.ADDRESS_INFO) &&
           this.completedSteps.has(WizardStep.OWNER_INFO);
  }
}
