/* Company Unified Add Component Styles - Wizard Design */

/* <PERSON>y Header */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: #f8f9fa;
  padding: 1rem 0;
  border-bottom: 1px solid #e9ecef;
}

/* Main Content */
.main-content {
  padding: 2rem 0;
  min-height: calc(100vh - 200px);
}

/* Modern Card Styles */
.modern-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  overflow: hidden;
}

.modern-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.modern-card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  border-bottom: none;
}

.modern-card-header h4,
.modern-card-header h5 {
  margin: 0;
  font-weight: 600;
}

.modern-card-body {
  padding: 2rem;
}

/* Step Navigation Buttons */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  color: white;
}

.btn-success:hover {
  background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.btn-outline-secondary {
  border: 2px solid #6c757d;
  color: #6c757d;
  background: transparent;
}

.btn-outline-secondary:hover {
  background: #6c757d;
  color: white;
  transform: translateY(-2px);
}

/* Modern Button Styles */
.modern-btn {
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modern-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modern-btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.modern-btn-secondary {
  background: #6c757d;
  color: white;
}

.modern-btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
}

.modern-btn-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.modern-btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.modern-btn-outline-primary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.modern-btn-outline-primary:hover:not(:disabled) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Form Styles */
.modern-form-group {
  margin-bottom: 1.5rem;
}

.modern-form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
}

.modern-form-control {
  width: 100%;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: #ffffff;
}

.modern-form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}

.modern-form-control.is-invalid {
  border-color: #dc3545;
}

.modern-form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

/* Material Form Field Styles */
.modern-mat-form-field {
  width: 100%;
}

.modern-mat-form-field .mat-form-field-wrapper {
  padding-bottom: 0;
}

.modern-mat-form-field .mat-form-field-outline {
  border-radius: 8px;
}

.modern-mat-form-field .mat-form-field-outline-thick {
  border-width: 2px;
}

.modern-mat-form-field.mat-focused .mat-form-field-outline-thick {
  border-color: #667eea;
}

/* Preview Styles */
.preview-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  height: 100%;
}

.preview-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e9ecef;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.preview-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.preview-item strong {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 600;
}

.preview-item span {
  font-size: 0.95rem;
  color: #495057;
  word-break: break-word;
}

/* Alert Styles */
.alert {
  border-radius: 8px;
  border: none;
  padding: 1rem 1.5rem;
}

.alert-info {
  background: linear-gradient(135deg, rgba(54, 209, 220, 0.1) 0%, rgba(91, 134, 229, 0.1) 100%);
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem 0;
  }

  .modern-card-body {
    padding: 1rem;
  }

  .modern-btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .preview-section {
    margin-bottom: 1rem;
  }

  .sticky-header .d-flex {
    flex-direction: column;
    gap: 1rem;
  }

  .sticky-header .d-flex .d-flex {
    flex-direction: row;
    justify-content: center;
  }
}
