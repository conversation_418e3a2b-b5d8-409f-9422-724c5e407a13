/* Company Unified Add Wizard Styles */

/* Sticky Header */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1020;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed) var(--transition-timing);
}

[data-theme="dark"] .sticky-header {
  background-color: var(--bg-primary);
  border-bottom-color: var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.main-content {
  padding-top: 0;
}

/* Step Navigation Buttons */
.btn {
  transition: all var(--transition-speed) var(--transition-timing);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Step Navigation Improvements */
.step-nav-btn {
  min-height: 80px;
  text-align: left;
  border: 2px solid transparent;
  transition: all var(--transition-speed) var(--transition-timing);
}

.step-nav-btn.active {
  border-color: var(--primary);
  background-color: var(--primary);
  color: white;
}

.step-nav-btn.completed {
  border-color: var(--success);
  background-color: var(--success-light);
  color: var(--success);
}

.step-nav-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Modern Button Styles */
.modern-btn {
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all var(--transition-speed) var(--transition-timing);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modern-btn-primary {
  background-color: var(--primary);
  color: white;
}

.modern-btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.modern-btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.modern-btn-secondary:hover:not(:disabled) {
  background-color: var(--secondary-dark);
  transform: translateY(-2px);
}

.modern-btn-success {
  background-color: var(--success);
  color: white;
}

.modern-btn-success:hover:not(:disabled) {
  background-color: var(--success-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.modern-btn-outline-primary {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.modern-btn-outline-primary:hover:not(:disabled) {
  background-color: var(--primary);
  color: white;
  transform: translateY(-2px);
}

.modern-btn-outline-secondary {
  background: transparent;
  color: var(--secondary);
  border: 2px solid var(--secondary);
}

.modern-btn-outline-secondary:hover:not(:disabled) {
  background-color: var(--secondary);
  color: white;
  transform: translateY(-2px);
}

/* Form Styles */
.modern-form-group {
  margin-bottom: var(--spacing-lg);
}

.modern-form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

.modern-form-control {
  width: 100%;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all var(--transition-speed) var(--transition-timing);
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.modern-form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem var(--primary-light);
  outline: none;
}

.modern-form-control.is-invalid {
  border-color: var(--danger);
}

.modern-form-control.is-invalid:focus {
  border-color: var(--danger);
  box-shadow: 0 0 0 0.2rem var(--danger-light);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger);
}

/* Material Form Field Styles */
.modern-mat-form-field {
  width: 100%;
}

.modern-mat-form-field .mat-form-field-wrapper {
  padding-bottom: 0;
}

.modern-mat-form-field .mat-form-field-outline {
  border-radius: var(--border-radius-md);
}

.modern-mat-form-field .mat-form-field-outline-thick {
  border-width: 2px;
}

.modern-mat-form-field.mat-focused .mat-form-field-outline-thick {
  border-color: var(--primary);
}

/* Preview Styles */
.preview-section {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  height: 100%;
  border: 1px solid var(--border-color);
}

.preview-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-color);
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.preview-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.preview-item strong {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.preview-item span {
  font-size: 0.95rem;
  color: var(--text-primary);
  word-break: break-word;
}

/* Alert Styles */
.alert {
  border-radius: var(--border-radius-md);
  border: none;
  padding: 1rem 1.5rem;
}

.alert-info {
  background-color: var(--info-light);
  color: var(--info);
  border-left: 4px solid var(--info);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem 0;
  }

  .modern-card-body {
    padding: 1rem;
  }

  .modern-btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .preview-section {
    margin-bottom: 1rem;
  }

  .sticky-header .d-flex {
    flex-direction: column;
    gap: 1rem;
  }

  .sticky-header .d-flex .d-flex {
    flex-direction: row;
    justify-content: center;
  }
}
