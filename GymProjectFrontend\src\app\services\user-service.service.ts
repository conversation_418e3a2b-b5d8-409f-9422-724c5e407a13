import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { BaseApiService } from './baseApiService';
import { User } from '../models/user';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UserService extends BaseApiService {

  constructor(private httpClient:HttpClient) {
    super();
  }
  getAll(): Observable<ListResponseModel<User>> {
    let newPath = this.apiUrl + 'user/getall';
    return this.httpClient.get<ListResponseModel<User>>(newPath);
  }

  getUserProfile(): Observable<any> {
    let newPath = this.apiUrl + 'user/profile';
    return this.httpClient.get<any>(newPath).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error(response.message || 'Profil bilgileri alınamadı');
      })
    );
  }

  getByEmail(email: string): Observable<any> {
    let newPath = this.apiUrl + 'user/getbyemail?email=' + email;
    return this.httpClient.get<any>(newPath);
  }

}
